# Changelog

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

### Unreleased

### Added

### Changed

### Removed

### Fixed

## Released
1.2.75 24-07-2025

### Added
[SIM-3215] - api-implement-location-history-api-integration
[SIM-3216] - api-implement-latest-data-session-api
[SIM-3217] - api-impliment-current-location-api-using-cell-information
[SIM-3271] - api-create-api-endpoint-to-get-most-recent-location-history-for-a-sim
### Changed

### Removed

### Fixed

## Released
1.2.74 16-07-2025

### Added

### Changed

### Removed

### Fixed

## Released
1.2.73 07-07-2025

### Added
[SIM-3279]-rate-plan-fixed-validation

### Changed
[SIM-3260] - Audit APIs URL change

### Removed

### Fixed

## Released
1.2.72 30-06-2025

### Added
[SIM-2853] - Improve Ordering Process

### Changed
[SIM-3212] - refactor_long_access_token_logic

### Removed

### Fixed
[SIM-3212] - Authorization_policy_fix
[SIM-3243] - get_policy_permission_fix
[SIM-3257] - Optimize POD SIM API and add logger

## Released
1.2.71 25-06-2025

### Added
[SIM-2853] - Improve Ordering Process

### Changed

### Removed

### Fixed

## Released
1.2.70 23-06-2025

### Added

### Changed
[SIM-3227] - Changed Voice `MO MT` definition to `MO & MT`

### Removed

### Fixed

## Released
1.2.69 20-06-2025

### Added
[SIM-3056] - add-new-form-factor-MFF2
[SIM-3052] - as-a-bt-user-i-want-to-see-info-about-invoice-was-unpublished-in-audit-trail-log-for-each-account
[SIM-2261] - as-a-bt-user-i-want-to-see-info-about-invoice-published-in-audit-trail-log-for-each-account
[SIM-1941] - as-a-bt-user-i-want-to-see-info-about-done-allocation-s-in-audit-trail-log-for-each-account
[SIM-2976] - Send cancel sim action SIM FLUSH.
[SIM-2977] - SIM POD
[SIM-2975] - SIM SMS
[SIM-3006] - SIM APN

### Changed

### Removed

### Fixed

## Released
1.2.68 18-06-2025

### Added

### Changed

### Removed

### Fixed
Fixed get_imsi_usage function to return correct data

## Released
1.2.67 12-06-2025

### Added
[SIM-1540] - Pagination, sorting and searching in Range
[SIM-3195] - Validation for rule creation and updation with valid unit

### Changed
[SIM-2767] - validations applied for the get Invoice
[SIM-590] - Get Invoice Pagination and searching update
[SIM-660] - Sorting by default add on is_published, is_billable and invoice_id
### Removed

### Fixed

## Released
1.2.66 10-06-2025

### Added
[SIM-2900] - Allow Voice and/or SMS `Monthly Pooled Usage` to be monitored
[SIM-2941] - Automation Rules improvements for `Cycle to Date Data Usage` rule

### Changed

### Removed

### Fixed

## Released
1.2.65 10-06-2025

### Added
[SIM-2954] - add-audit for big data processing in sim

### Changed

### Removed

### Fixed

## Released
1.2.64 02-06-2025

### Added

### Changed
[SIM-3045] - Create role optimization
### Removed

### Fixed

## Released
1.2.63 29-05-2025

### Added
[SIM-3159] publish multiple invoice
[SIM-3048] - Socket implementation for the real time updates and performance improvements.

### Changed

### Removed

### Fixed

## Released
1.2.62 20-05-2025

### Added
[SIM-3115] - api-create-sim-order-post-api
### Changed

### Removed

### Fixed
[SIM-3039] - Get sim card usage api optimization and account count summary api added 

## Released
1.2.61 16-05-2025

### Added
[SIM-3072] - added loggers in process_saf_event api
[SIM-3062] - Unallocation of Imsis api
[SIM-3063] - Delete Unallocated Imsis api

### Changed

### Removed

### Fixed

## Released
1.2.60 05-05-2025

### Added
[SIM-2935] - trivy_fix poetry packages updated for virtualenv, setuptools, python, pillow, gunicorn, gitpython, certifi, opentelemetry-instrumentation, protobuf
### Changed

### Removed

### Fixed

## Released
1.2.59 01-05-2025

### Added
[SIM] - add-adjustment-type

### Changed

### Removed

### Fixed

## Released
1.2.58 23-04-2025

### Added

### Changed
[SIM-2963] - Billing url changes

### Removed

### Fixed

## Released
1.2.57 22-04-2025

### Added

### Changed
[SIM-2979] - Single sim activate and Bulk Sim process validation and schema changes.

### Removed

### Fixed
[SIM-2963] - Billing performance fix

## Released
1.2.56 15-04-2025

### Added
[SIM-2702] -  Dynamic partition creation
### Changed

### Removed

### Fixed

## Released
1.2.55 11-04-2025

### Added
[SIM-2967] -  Exception handeling naming change in sim module
### Changed
[SIM-2968] - Monitoring variables naming change
### Removed

### Fixed
[SIM-2888] - Fix list accounts performance issue

## Released
1.2.54 08-04-2025

### Added

### Changed

### Removed

### Fixed
[SIM-2909] - Validation on MSISDN update with same group of type and profile

## Released
1.2.53 02-04-2025

### Added
[SIM-2909] - Added API to bulk MSISDN update with checkbox

### Changed

### Removed

### Fixed
[SIM-2930] - Handled 500 exception for ClientAdmin

## Released
1.2.52 01-04-2025

### Added
[SIM-2932] - Update sim profile to `VOICE_SMS_DATA` from `DATA_ONLY`

### Removed

### Fixed

## Released
1.2.51 27-03-2025

### Added
[SIM-2667] - added sim profile in account table
[SIM-2668] - export msisdn endpoint added counts for msisdn
[SIM-2668] - upload and get msisdn from msisdn_pool table and create and get api endpoints added with new msisdn_pool table

### Changed

### Removed

### Fixed

## Released
1.2.50 17-03-2025

### Added

### Changed
[SIM-2895] - Made rateplan action non-mandatory for monthly pool rule category creation/updation

### Removed

### Fixed

## Released
1.2.49 05-03-2025

### Added
[SIM-2808] - Add or check Automation rule type for monthly_rule, Add Code for the respective master values as well
[SIM-2809] - Add new Automation rule category for monthly_rule, Add Code for the respective master values as well
[SIM-2810] - Add new Automation rule definition for monthly_rule, Add Code for the respective master values as well
[SIM-2812] - extend_create_update_rule_api_for_monthly_rule
[SIM-2810] - Automation Rule Definition based on Ryle Category

### Changed

### Removed

### Fixed
[SIM-2660] - bandit and semgrep security issue resolved

## Released
1.2.48 17-02-2025

### Added

### Changed
[SIM-2856] - rate_plan_by_accounts_account_id_changes

### Removed

### Fixed

## Released
1.2.47 14-02-2025

### Added
[SIM-2607] - Rate plan change implementation in automation rule
[SIM-2780] - Added change plan validation.

### Changed

### Removed

### Fixed

## Released
1.2.46 10-02-2025

### Added

### Changed

### Removed

### Fixed

## Released
1.2.45 06-02-2025

### Added

### Changed
[SIM-2836] - rate_plan_changes_optimization, views added and postgres functions modified
### Removed

### Fixed
[SIM-2660] - bandit sql injection issue resolved

## Released
1.2.44 31-01-2025

### Added
rate_plan_bug_fix

### Changed

### Removed

### Fixed

## Released
1.2.43 30-01-2025

### Added
[SIM-2771] - create_patch_api_for_setting_default_rate_plan
[SIM-82] - set default rate plan
[SIM-2722] - while getting rate plan details keep the default rate plan at first
[SIM-2734] - change get api for rate plan while reallocation
[SIM-2724] - change get api for rate plan while allocation
[SIM-2649] - Lock Unlock feature
[SIM-2773] - new call added for lock unlock status update

### Changed
[SIM-2801] - code_optimization_cdr

### Removed

### Fixed

## Released
1.2.42 22-01-2025

### Added
[SIM-2768] - stop_downgrade_alembic
[SIM-2781] - Connection history code improvement in the imsi side on service layer
[SIM-2781] - remove_unwanted_table_and_code_improvements

### Changed

### Removed

### Fixed

### Released
1.2.41 08-01-2025

### Added

### Changed

### Removed

### Fixed
Fixed error cdr of 0 bytes

## Released
1.2.40 08-01-2025

### Added
[SIM-2700] - Indexes-Partition on cdr related tables
[SIM-2692] - scripts creation and partitions creation with optimized queries for cdr
[SIM-2745] - external push cdr api chnages
### Changed

### Removed

### Fixed

## Released
1.2.39 01-01-2025

### Added
[SIM-2728] configure_ordering_in_automation_and_add_pagination_in_get_all_rate_plans

### Changed

### Removed

### Fixed

## Released
1.2.38 01-01-2025

### Added

### Changed

### Removed

### Fixed
[SIM-2063] - Billing RatePlan Prod Bug Fix

## Released
1.2.37 23-12-2024

### Added
[SPOG-2246] - Added x-trace-id in all APIs
[SIM-2648] - improvement-and-feedback-for-automation-rule-and-rate-plans
[SIM-2687] - Reallocation same account rate plan audit log
[SIM-2409] - Added API to update bulk MSISDN

### Changed

### Removed

### Fixed
[SIM-2538] - Bugfix billing export functionality

## Released
1.2.36 16-12-2024

### Added

### Changed

### Removed

### Fixed
[SIM-2708] - notification_endpoint_fix getting 5xx error in API when triggering Active and De-Active Sim API from Non-Prod ENV
## Released
1.2.35 27-11-2024

### Added
[SPOG-2548] - Redis implementation, performance improvements
[SIM-2634] - SIM-2634-corrections-for-decimal-places-for-rates-inside-rate-plans
[SIM-2639] - add_decimal_validation_in_rate_plan
[SIM-2633] - sim-limit-validations
[SIM-2638] - add_sim_limit_to_rateplan_by_id, update rateplan, allocation and reallocation

### Changed

### Removed

### Fixed

## Released
1.2.34 19-11-2024

### Added
[SIM-2597] - allow_crud_operation_to_admin
[SIM-2242] - Allow Admin to edit rule
[SIM-2241] - Allow Admin to create rule
[SIM-2549] - allowance used_for_each_rate_plan to display in client and distributor rateplan view
[SIM-2629] - allowance used_for_each_rate_plan in fixed and flexi pool only not in PAYG or Individual
[SIM-2592] - validation_improvements_rate_plans
### Changed

### Removed

### Fixed

## Released
1.2.33 07-11-2024

### Added

### Changed

### Removed

### Fixed
[SIM-2588] - New Billing SimFee Logic for account not on per ratemodel sim.

## Released
1.2.32 06-11-2024

### Added

### Changed
[SIM-2584] - Commercial models new billing logic
### Removed

### Fixed

## Released
1.2.31 05-11-2024

### Added
SIM_billing_rate_plan_bug_fix for isoverage condition add in function invoice_overage_charge_details
[SIM-2575] - SIM_reallocation_update_bug_fix modified sim_reallocation function with updating sim_card.rate_plan_id
### Changed

### Removed

### Fixed

## Released
1.2.30 05-11-2024

### Added
[SIM-2437] - create a Rate Plan with 'flexible pool' model
[SIM-620] - create a Rate Plan with 'fixed pool' model
[SIM-2450] - create a Rate Plan with 'Individual Plan' model
[SIM-2545] - Calculations for 'Fixed Pool' model
[SIM-2546] - Calculations for 'Flexible Pool' model
[SIM-2502] - Calculations for 'Individual Plan' model
[SIM-2536] - Validation Rate Plan to the individual model
[SIM-2535] - Create and modify Rate Plan individual model
[SIM-2540] - Create and modify Rate Plan fixed pool model
[SIM-2539] - Validation Rate Plan to the Fixed pool model
[SIM-2539] - Validation Rate Plan to the flexible model
[SIM-2539] - Create and modify Rate Plan for flexible pool model
### Changed

### Removed

### Fixed

## Released
1.2.29 24-09-2024

### Added
License.md file added

### Changed

### Removed

### Fixed
[SPOG-2559] - Export billing timeoutfix

## Released
1.2.28 24-09-2024

### Added
License.md file added
### Changed

### Removed

### Fixed
[SPOG-2559] - Export billing timeoutfix


## Released
1.2.27 18-09-2024

### Added

### Changed
[SPOG-2447] - Change simusagelimit to camelCase

### Removed

### Fixed

## Released
1.2.26 08-08-2024

### Added

### Changed

### Removed

### Fixed
[SPOG-2479] - Duplicate CDR validation only for gsm-data type

## Released
1.2.25 08-08-2024

### Added
[SPOG-2456] - rate_plan_by_accounts_prod_issue
### Changed

### Removed

### Fixed

## Released
1.2.24 06-08-2024

### Added
[SPOG-2427] - need-to-reverse-entry-when-we-received-work-item-as-failed-status
[SPOG-2187] - Audit trail log (allocation, re-allocation, activate, deactivate, get_sim_status, get sim card audit log, get system audit log)
### Changed

### Removed

### Fixed

## Released
1.2.23 25-07-2024

### Added
[SPOG-1934] - automation-rules-permissions
[SPOG-2467] - SPOG-1934 SPOG_automation_rule_authorization_permission. script automation_rule_role.py written
### Changed

### Removed

### Fixed

## Released
1.2.22 23-07-2024

### Added

### Changed

### Removed

### Fixed
[SPOG-2441] - duplicate cdr bucket implemented

## Released
1.2.21 23-07-2024

### Added
[SPOG-2171] - automation-database-need-to-create-migration-script
[SPOG-2157] - create rule
[SPOG-2163] - get rule by id
[SPOG-2247] - rule type
[SPOG-2253] - rule name by category id
[SPOG-2258] - rule definition by rule name
[SPOG-2160] - rule name type definition
[SPOG-2161] - get action and notification
[SPOG-2158] - get rules
[SPOG-2314] - (user story) All rules related tickets covered from backend side.

### Changed
[SPOG-2441] - duplicate cdr bucket implemented

### Removed

### Fixed

## Released
1.2.20 22-07-2024

### Added
[SPOG-2171] - automation-database-need-to-create-migration-script
[SPOG-2157] - create rule
[SPOG-2163] - get rule by id
[SPOG-2247] - rule type
[SPOG-2253] - rule name by category id
[SPOG-2258] - rule definition by rule name
[SPOG-2160] - rule name type definition
[SPOG-2161] - get action and notification
[SPOG-2158] - get rules
[SPOG-2314] - (user story) All rules related tickets covered from backend side.

### Changed
[SPOG-2441] - duplicate cdr bucket implemented

### Removed

### Fixed

## Released
1.2.19 18-06-2024

### Added

### Changed

### Removed

### Fixed
[SPOG-2426] - script added to update sim status

## Released
1.2.18 13-06-2024

### Added
[SPOG-1980] - email-id-to-lower-in-post-apis
[SPOG-2414] Configure GitLab Merge Request Template

### Changed
[SPOG-2047] - api-able-to-create-blank-empty-name-rate-plan
[SPOG-2387] - Improvements generate token api

### Removed

### Fixed
[SPOG-2365] - Fix get, create, and delete role APIs
Code improvisation of get and create role
SPOG-rateplan-bug-fix (get rate-plan endpoint fixed with getting all the rate-plans)

## Released
1.2.17 21-05-2024

### Added

### Changed

### Removed

### Fixed
Code improvisation of get and create role

## Released
1.2.16 20-05-2024

### Added
[SPOG-2282] - Generate access token

### Changed
[SPOG-2067] connection_history_performance_issue, Refractored all export endpoints except billing export.
### Removed

### Fixed

## Released
1.2.15 26-04-2024

### Added

### Changed

### Removed

### Fixed
[SPOG-2359] - Reallocation bug fixes

## Released
1.2.14 22-04-2024

### Added
[SPOG-2341] - SIM status pagination issue logging
[SPOG-2312] - Get duplicate cdr for the month
[SPOG-2246] - Download duplicate CDR

### Changed

### Removed

### Fixed

## Released
1.2.13 11-04-2024

### Added
[SPOG-2332] - Re_allocation bug
### Changed

### Removed

### Fixed

## Released
1.2.12 05-04-2024

### Added
[SPOG-2029] - Re_allocation between account
[SPOG-2070] - Support multiple columns CSV file.
[SPOG-2275] - Add permission on reallocation API
[SPOG-2267] - api-create-api-to-get-sim-information
### Changed

### Removed

### Fixed

## Released
1.2.11 29-03-2024

### Added

### Changed
[SPOG-2264] - Update existing ReadOnly roles to set is_default = true

### Removed

### Fixed

## Released
1.2.10 28-03-2024

### Added

### Changed
[SPOG-2264] - Update existing ReadOnly roles to set is_default = false

### Removed

### Fixed

## Released
1.2.9 28-03-2024

### Added
[SPOG-2264] - Ignore DU, CU role. Add ReadOnly roles

### Changed

### Removed

### Fixed

## Released
1.2.8 19-03-2024

### Added

### Changed
[SPOG-2264] - Allow everyone to see account names (API)

### Removed

### Fixed

## Released
1.2.7 14-03-2024

### Added
[SPOG-2197] - As-a-developer-i-want-to-see-the-database-history
### Changed

### Removed

### Fixed
[SPOG-2213] - Bugfix rolename in description

## Released
1.2.6 29-02-2024

### Added
[SPOG-1916] - CREATE, GET, DELETE role with database and keycloak
[SPOG-1925] - Restrict API access to unauthorized user
[SPOG-2010] - get only roles available in database

[SPOG-1992] - api-create-an-endpoint-to-obtain-the-access-barrier-token
[SPOG-2061] - validation-sim-validation-account-wise-on-activate-deactivate-and-sim-status-api
[SPOG-2121] - Validation on default role deletion, email in created_by field
[SPOG-1596] - Create role for `My Organization` or `Account`

### Changed
[SPOG-1931] - api-role-management-authorization-search-api
[SPOG-1976] - Restrict to update and delete default roles
[SPOG-2131] - OpenAPI url change to `/v1/api/token`

### Removed

### Fixed
[SPOG-2121] - Get default role as Admin, User, fix searching issue
[SPOG-2121] - Get `N/A` in description if no description was given

## Released
1.2.5 21-02-2024

### Added

### Changed
[SPOG-2113] - do-not-create-files-in-local

### Removed

### Fixed
[SPOG-2076] - need-to-remove-sms-value-0-in-reconciliation
## Released
1.2.4 13-02-2024

### Added
[SPOG-2100] - Sim-reallocation-script and function

### Changed

### Removed

### Fixed

## Released
1.2.3 12-02-2024

### Added
[SPOG-2100] - Sim-reallocation-script and function

### Changed

### Removed

### Fixed
[SPOG-2108] - Fix pushcdr 422 error, Add error CDRs into a saperate folder

## Released
1.2.2 01-02-2024

### Added

### Changed

### Removed

### Fixed
[SPOG-2089] - SIM card charge on new sim issue fix
## Released
1.2.1 30-01-2024

### Added

### Changed
[SPOG-2075] - Billing changes with updated logic

### Removed

### Fixed
[SPOG-2076] - monthly-reconciliation-not-work-as-expected
### Released
1.2.0 18-01-2024

### Added

### Changed

### Removed

### Fixed

## Released
1.1.42 18-01-2024

### Added
[SPOG-2022] - Script to add missing monthly sim data on prod
[SPOG-2025] - reconciliation-with-monthly-file
### Changed
[SPOG-2023] - Revert Error log changes

### Removed

### Fixed
[SPOG-2023] - Fix error log issue

## Released
1.1.41 05-01-2024

### Added
[SPOG-1989] - Add - Create two new API Endpoint to to export SMS and voice connection history
[SPOG-1982] - api-create-new-endpoint-for-invoice-reconciliation
[SPOG-1945] - Store CSV file in S3 bucket
[SPOG-1947] - SIM-wise error message for allocation
[SPOG-1986] - Add pagination to allocation api
[SPOG-1948] - Add individual records into allocation table
### Changed


### Removed

### Fixed
[SPOG-2008] - Bugfix month error in copy_sim_monthly_data

## Released
1.1.39 01-12-2023

### Added
[SPOG-1845] - SIM allocation based on custom input
[SPOG-1846] - SIM allocation validation functions
[SPOG-1847] - Allocation table changes
[SPOG-1848] - Ranges and allocation api changes
[SPOG-1771] - Added `assign roles to group` endpoint
[SPOG-1768] - Added `get resources, scopes, and permissions` endpoint
[SPOG-1738] - Added `get roles` endpoint
[SPOG-1879] - Added `get role permission` endpoint
[SPOG-1896] - Added pagination
[SPOG-1879] - Added role_id, name, and description in role's permission api
[SPOG-1922] - Migration to copy monthly statistics, hide authorization APIs

### Changed
[SPOG-1865] - order-by-usage-ms-report-reverse-order
### Removed

### Fixed

## Released
1.1.37 12-10-2023

### Added
[SPOG-1770] - Get group role by organization id

### Changed

### Removed

### Fixed
[SPOG-1830] - Support string type for sen_to and sent_from columns
[SPOG-1830] - Support string type for call_number for voice cdr
## Released
1.1.36 06-10-2023

### Added
[SPOG-1766] - Get user's scope with service-to-service authentication
[SPOG-1768] - Get group members

### Changed

### Removed

### Fixed
[SPOG-1788] - IMSI Exception message
[SPOG-1792] - Fix data rendering issue on SIM Mgt
[SPOG-1802] - total-usage-market-imsi
[SPOG-1808] - get-market-share-imsi-bug-fix
## Released
1.1.35 03-10-2023

### Added
[SPOG-1763] - Refactor Voice & SMS connection history
[SPOG-1379] - Carriername & Countryname added in response
[SPOG-1763] - Sorting and searching for voice and sms
### Changed

### Removed

### Fixed
[SPOG-1789] - bugfix get SIM usage

## Released
1.1.34 26-09-2023

### Added

### Changed

### Removed

### Fixed

## Released
1.1.33 25-09-2023

### Added

### Changed

### Removed

### Fixed
[SPOG-1669] - connection-history-by-imsi-bug

## Released
1.1.32 21-09-2023

### Added
[SPOG-1686] - Update post cdr
[SPOG-1659] - SMS connection history
[SPOG-1718] - carrier-imsi-market-share-get-api
### Changed

### Removed

### Fixed
[SPOG-1613] - sales-channel-sorting-bug-fixed
## Released
1.1.31 15-09-2023

### Added

### Changed

### Removed

### Fixed
[SPOG-1678] - Bugfixed notification auth issue
[SPOG-1674] - refactor-code-optimisation
[SPOG-1724] - Bugfixed get Market Share usage by account
[SPOG-1750] - Raise 404 Not found

## Released
1.1.30 12-09-2023

### Added
[SPOG-1551] - Added service to service authentication
[SPOG-1659] - Voice connection history
[SPOG-1667] - excel-to-msisdn-update-sim
### Changed

### Removed

### Fixed
[SPOG-1673] - Bugfix carrier validation
[SPOG-1672] - Bugfix sim usage 401 unauthorized

## Released
1.1.29 08-09-2023

### Added
[SPOG-1613] - sorting-account-column-page
[SPOG-1481] - Traffic-below-EE-Traffic-Commitment-in-Billing-Settings
[SPOG-1516] - ee-column-add-sim-usage
[SPOG-1642] - Add positive and negative test cases
### Changed
[SPOG-1603] - change billing settings create/update account
[SPOG-1657] - Change Market Share Response by carrier_name
### Removed

### Fixed
[SPOG-1657] - bug fix if carrier name not available
[SPOG-1516] - authentication related to bug fix 
[SPOG-1639] - Fix raise exception issue
[SPOG-1646] - Bugfix total usage issue

## Released
1.1.28 28-08-2023

### Added
[SPOG-1491]- change environment variable `MARKET_SHARE_BASE_URL` to `APP_BASE_URL`
[SPOG-1491]- Call cdr analytics api to store cdr data into mongodb
[SPOG-1432]- Search rate plan by account name, searching and pagination on accounts
Devops Changes
[SPOG-1589]- sim-service-business-logic-test-cases
[SPOG-1591]- Migration to add cdr_object_id into cdr  
[SPOG-1500]- Add - Create API Endpoints to get marketshare data from analysis service   

### Changed

### Removed

### Fixed
[SPOG-1491] - Bugfix app version

## Released
1.1.27 17-08-2023

### Added

### Changed

### Removed

### Fixed

## Released
1.1.26 17-08-2023

### Added

### Changed

### Removed

### Fixed
[SPOG-1491]- Bugfix app version
[SPOG-1452]- Total line is updated on filter bug fixed

## Released
1.1.24 11-08-2023

### Added
[SPOG-1496] - Account id validation on rate plan create/update
### Changed

### Removed

### Fixed

## Released
1.1.23 10-08-2023

### Added

### Changed

### Removed

### Fixed
alembic fix

## Released
1.1.22 10-08-2023

### Added
[SPOG-1452]- Total line is updated on filter
[SPOG-1550]- sim-business-logic-test-cases
[SPOG-1491]- Added app version related APIs

### Changed

### fixed
[SPOG-1558]- Fix saf invite user issue

### Removed

### Released
1.1.21 28-07-2023 

### Added
[SPOG-1381]- Added SIM activity table
[SPOG-1494]- add-column-in-useractivitylog-table
[SPOG-1494]- Add-foreignkey-constrains-useractivitylog
[SPOG-1383]- Added User activity table
[SPOG-1522]- Added package `bandit = "1.7.5"`
[SPOG-1525]- Add API Endpoint to featch ICCID data
[SPOG-1534]- Add enum `Other` into industry_vertical type

### Changed
[SPOG-1381]- refactor providerlog sim status
### Fixed
[SPOG-1535]- sim-status-sorting-error
[SPOG-1533]- account-imsis-not-found-bug
[SPOG-1442]- Display Carier name instead
[SPOG-1492]- Audit Sorting issue
### Remove
[SPOG-1444]- Remove Saf Script
[SPOG-1383]- Added User activity table
## Released
1.1.20 13-07-2023

### Added

### Changed

### Fixed
- [SPOG-1442] - Display carrier name instead

### Remove
- [SPOG-1444] - Remove-saf-script
## Released
1.1.19 11-07-2023 with custom xlsx 

### Added
- [SPOG-1404] - Connection History: Display Career name, not TAGID

### Changed

### Fixed
- [SPOG-1345] - Connection History: total count fixed

### Remove

## Released
1.1.18 05-07-2023


### Added
- [SPOG-1324] - Create-event-listener-service 

### Fixed
- [SPOG-1405] - 447 Prefix updates
- [SPOG-1359] - Bugfix bulk activate and activate SIMs


### Changed

### Removed
1.1.17 04-07-2023




### Added
- [SPOG-1388] - Add a new table for carrier name

### Fixed
- [SPOG-1417] - Bugfix incorrect paginated response

### Changed
- [SPOG-1416] - Push CDR SMS profile support
- [SPOG-1390] - Update datatype

### Removed

## Released
1.1.16 30-06-2023

### Added
- [SPOG-1350] - Synchronize SIM status
- [SPOG-1366] - SIM charge applied to published invoices

### Fixed
- [SPOG-1329] - Bugfix `ready for activation` sim status


### Changed
- [SPOG-1387] - Connection History API to display country name instead of ISO code

### Removed


## Released
1.1.15 29-06-2023

### Added
- [SPOG-1351] - monitoring-grafana-prometheus
- [SPOG-1357] - create endpoint to copy the monthly data
- [SPOG-1359] - add-endpoint to perform bulk activate and deactivate SIMs
- [SPOG-1274] - add-changelog-file-backend


### Fixed


### Changed


### Removed


## Released
