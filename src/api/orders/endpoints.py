from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, Request
from pydantic import ValidationError
from starlette import status

import api.deps as trace_id_deps
from api.authorization.deps import authorization_service
from api.decorators import check_permissions
from api.orders import deps
from api.orders.schemas import (
    CreateOrderRequest,
    OrderDetailsResponse,
    OrdersDataResponse,
    UpdateOrderStatusRequest,
    UpdateOrderStatusResponse,
)
from api.schema_types import PaginatedResponse
from app.config import logger
from auth.exceptions import ForbiddenError, Unauthorized
from authorization.domain.ports import AbstractAuthorizationAPI
from common.ip_utils import get_client_ip
from common.ordering import Ordering
from common.pagination import InvalidPage, Pagination
from common.parser import ParsingError
from common.searching import Searching
from orders.adapters.exceptions import StatusError
from orders.domain.model import OrderResponse
from orders.exceptions import NoOrdersLogs
from orders.services import AbstractOrdersService
from sim.exceptions import NotFound

router = APIRouter(tags=["orders"])


@router.post("/orders", status_code=status.HTTP_201_CREATED)
@check_permissions
def create_order(
    request: Request,
    order: CreateOrderRequest,
    orders_service: AbstractOrdersService = Depends(deps.orders_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> OrderResponse:
    try:
        # Convert CreateOrderRequest to OrderRequest before passing to orders_service
        client_ip = get_client_ip(request)
        order_model = order.to_model()
        return orders_service.create_order(
            order=order_model,
            client_ip=client_ip,
        )
    except ParsingError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except ValueError as e:
        logger.error(f"Value error occoured.: {e}")
        raise HTTPException(
            status.HTTP_422_UNPROCESSABLE_ENTITY,
            "We are unable to process your request.",
        )
    except ValidationError as e:
        logger.error(f"Validation error in create order request: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(e),
        )
    except Unauthorized as e:
        logger.error(f"Unauthorized.: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Unauthorized access."
        )
    except ForbiddenError as e:
        logger.error(f"Forbidden:{str(e)}")
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Forbidden.")
    except Exception as e:
        logger.error(f"Failed to create order - {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Couldn't process the order request",
        )


@router.patch("/orders/{order_uuid}/status", status_code=status.HTTP_200_OK)
@check_permissions
def update_order_status(
    request: Request,
    order_uuid: UUID,
    update_request: UpdateOrderStatusRequest,
    orders_service: AbstractOrdersService = Depends(deps.orders_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> UpdateOrderStatusResponse:
    try:
        logger.info(
            f"Updating SIM order {order_uuid} status to " f"{update_request.status}"
        )
        client_ip = get_client_ip(request)
        return orders_service.update_order_status(
            order_uuid=order_uuid,
            update_data=update_request,
            client_ip=client_ip,
        )
    except NotFound as e:
        logger.error((f"{str(e)}"))
        logger.error(f"Order Data Not Found. : {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Data Not Found.",
        )
    except ForbiddenError as e:
        logger.error((f"=========forbiddenerror:{str(e)}"))
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied.",
        )
    except Unauthorized as e:
        logger.error(f"Unauthorized.: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Unauthorized access."
        )
    except StatusError as e:
        logger.error(f"Not Allowed.: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except ParsingError as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except ValueError as e:
        logger.error(f"Value error occurred: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except Exception as e:
        logger.error(f"Failed to update order status: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Couldn't update the order status",
        )


@router.get(
    "/orders",
    status_code=status.HTTP_200_OK,
    response_model=PaginatedResponse[OrdersDataResponse],
)
@check_permissions
def get_orders(
    request: Request,
    account_id: int | None = None,
    orders_service: AbstractOrdersService = Depends(deps.orders_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    pagination: Pagination = Depends(Pagination.query()),
    ordering: Ordering = Depends(
        Ordering.query(
            OrdersDataResponse,
            default_ordering="-orderDate",
            ordering_fields=(
                "status",
                "orderId",
                "orderUuid",
                "orderDate",
                "customerAccountName",
                "personPlacingOrder",
                "customerPhone",
                "orderBy",
                "accountId",
                "customerId",
                "customerEmail",
            ),
        )
    ),
    searching: Searching
    | None = Depends(
        Searching.build(
            fields={
                "status",
                "order_date",
                "order_id",
                "customers.order_uuid",
                "customers.customer_account_name",
                "customers.person_placing_order",
                "customers.customer_contact_no",
                "customers.customer_account_id",
                "customers.customer_id",
                "customers.customer_email",
            },
        )
    ),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> PaginatedResponse[OrdersDataResponse]:
    try:
        records, total_count = orders_service.get_orders(
            account_id=account_id,
            pagination=pagination,
            ordering=ordering,
            searching=searching,
        )
        return PaginatedResponse.from_iterable(
            pagination=pagination,
            results=list(map(OrdersDataResponse.from_model, records)),  # type: ignore
            total_count=total_count,
        )
    except NotFound as e:
        logger.error((f"{str(e)}"))
        logger.error(f"Order Data Not Found. : {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Data Not Found.",
        )
    except ForbiddenError as e:
        logger.error((f"Order Data Access denied. {str(e)}"))
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied.",
        )
    except Unauthorized as e:
        logger.error(f"Unauthorized.: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Unauthorized access."
        )
    except NoOrdersLogs as e:
        logger.error(f"No order logs error occoured.:{str(e)} ")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="No order logs found."
        )
    except ValueError as e:
        logger.error(f"value error occoured.:{str(e)} ")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except ParsingError as e:
        logger.error(f"Parsing error: {str(e)}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))
    except HTTPException as e:
        logger.error(f"HTTP exception occurred: {str(e)}")
        raise e
    except InvalidPage as e:
        logger.error(f"Invalid page error.: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST, detail=f"Invalid page: {str(e)}"
        )
    except AssertionError as e:
        logger.error(f"Assertion error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid request parameters.",
        )
    except (Exception) as e:
        logger.error(f"Unexpected error: {str(e)}")
        raise HTTPException(
            status.HTTP_400_BAD_REQUEST,
            "We couldn't process your request.",
        )


@router.get("/orders/{order_uuid}", status_code=status.HTTP_200_OK)
@check_permissions
def get_order_details(
    request: Request,
    order_uuid: UUID,
    orders_service: AbstractOrdersService = Depends(deps.orders_service),
    authorization: AbstractAuthorizationAPI = Depends(authorization_service),
    trace_id: UUID = Depends(trace_id_deps.get_trace_id),
) -> OrderDetailsResponse:
    try:
        logger.info(f"Fetching details for order {order_uuid}")
        order_details = orders_service.get_order_details(order_uuid=order_uuid)
        if not order_details:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Order not found"
            )
        return OrderDetailsResponse.from_model(order_details)
    except NotFound as e:
        logger.error((f"{str(e)}"))
        logger.error(f"Order Data Not Found. : {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Data Not Found.",
        )
    except ForbiddenError as e:
        logger.error((f"{str(e)}"))
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied.",
        )
    except Unauthorized as e:
        logger.error(f"Unauthorized.: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED, detail="Unauthorized access."
        )

    except ParsingError as e:
        logger.error(f"Parsing error: {str(e)}")
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))

    except Exception as e:
        logger.error(f"Failed to fetch order details: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Couldn't fetch the order details",
        )
